#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
平台操作详细分析
分析各平台下的具体操作行为模式
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False
except:
    print("警告: 无法设置中文字体，图表可能显示乱码")

class PlatformActionAnalyzer:
    def __init__(self, data_path):
        """初始化平台操作分析器"""
        self.data_path = data_path
        self.df = None
        self.load_data()
        
    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        try:
            self.df = pd.read_csv(self.data_path)
            print(f"数据加载成功，共{len(self.df)}条记录")
            
            # 数据预处理
            self.df['created_time'] = pd.to_datetime(self.df['created_time'])
            self.df['date'] = self.df['created_time'].dt.date
            self.df['hour'] = self.df['created_time'].dt.hour
            
            # 处理缺失值
            self.df['via'] = self.df['via'].fillna('Unknown')
            self.df['action_type'] = self.df['action_type'].fillna('unknown')
            
            print("数据预处理完成")
            
        except Exception as e:
            print(f"数据加载失败: {e}")
    
    def analyze_platform_actions(self):
        """平台操作交叉分析"""
        print("\n=== 平台操作交叉分析 ===")
        
        # 创建平台-操作交叉表
        platform_action_crosstab = pd.crosstab(
            self.df['via'], 
            self.df['action_type'], 
            margins=True
        )
        
        print("平台-操作交叉统计表:")
        print(platform_action_crosstab)
        
        # 计算各平台操作占比
        platform_action_pct = pd.crosstab(
            self.df['via'], 
            self.df['action_type'], 
            normalize='index'
        ) * 100
        
        print("\n各平台操作类型占比(%):")
        print(platform_action_pct.round(2))
        
        # 计算各操作在不同平台的分布
        action_platform_pct = pd.crosstab(
            self.df['via'], 
            self.df['action_type'], 
            normalize='columns'
        ) * 100
        
        print("\n各操作类型在不同平台的分布(%):")
        print(action_platform_pct.round(2))
        
        return platform_action_crosstab, platform_action_pct, action_platform_pct
    
    def analyze_platform_user_behavior(self):
        """平台用户行为分析"""
        print("\n=== 平台用户行为深度分析 ===")
        
        # 各平台用户行为统计
        platform_user_stats = self.df.groupby(['via', 'action_type']).agg({
            'member_id': 'nunique',
            'id': 'count'
        }).reset_index()
        platform_user_stats.columns = ['平台', '操作类型', '用户数', '操作次数']
        platform_user_stats['人均操作次数'] = platform_user_stats['操作次数'] / platform_user_stats['用户数']
        
        print("各平台各操作详细统计:")
        for platform in platform_user_stats['平台'].unique():
            print(f"\n--- {platform} 平台 ---")
            platform_data = platform_user_stats[platform_user_stats['平台'] == platform]
            platform_data = platform_data.sort_values('操作次数', ascending=False)
            print(platform_data[['操作类型', '用户数', '操作次数', '人均操作次数']].to_string(index=False))
        
        return platform_user_stats
    
    def analyze_platform_action_trends(self):
        """平台操作时间趋势分析"""
        print("\n=== 平台操作时间趋势分析 ===")
        
        # 按日期、平台、操作类型统计
        daily_platform_action = self.df.groupby(['date', 'via', 'action_type']).agg({
            'id': 'count',
            'member_id': 'nunique'
        }).reset_index()
        daily_platform_action.columns = ['日期', '平台', '操作类型', '操作次数', '用户数']
        
        # 各平台主要操作的日趋势
        main_actions = ['login', 'register', 'report', 'kyc']
        
        for action in main_actions:
            if action in self.df['action_type'].values:
                print(f"\n--- {action.upper()} 操作各平台日趋势 ---")
                action_trend = daily_platform_action[daily_platform_action['操作类型'] == action]
                action_pivot = action_trend.pivot_table(
                    index='日期', 
                    columns='平台', 
                    values='操作次数', 
                    fill_value=0
                )
                print(action_pivot.tail(7))  # 显示最近7天
        
        return daily_platform_action
    
    def analyze_platform_conversion(self):
        """平台转化分析"""
        print("\n=== 平台用户转化分析 ===")
        
        # 计算各平台的用户转化情况
        platform_conversion = []
        
        for platform in self.df['via'].unique():
            if platform != 'Unknown':
                platform_data = self.df[self.df['via'] == platform]
                
                # 统计各操作用户数
                register_users = len(platform_data[platform_data['action_type'] == 'register']['member_id'].unique())
                login_users = len(platform_data[platform_data['action_type'] == 'login']['member_id'].unique())
                report_users = len(platform_data[platform_data['action_type'] == 'report']['member_id'].unique())
                kyc_users = len(platform_data[platform_data['action_type'] == 'kyc']['member_id'].unique())
                total_users = len(platform_data['member_id'].unique())
                
                # 计算转化率
                login_rate = (login_users / total_users * 100) if total_users > 0 else 0
                report_rate = (report_users / total_users * 100) if total_users > 0 else 0
                kyc_rate = (kyc_users / total_users * 100) if total_users > 0 else 0
                
                platform_conversion.append({
                    '平台': platform,
                    '总用户数': total_users,
                    '注册用户数': register_users,
                    '登录用户数': login_users,
                    '报告用户数': report_users,
                    'KYC用户数': kyc_users,
                    '登录转化率(%)': round(login_rate, 2),
                    '报告转化率(%)': round(report_rate, 2),
                    'KYC转化率(%)': round(kyc_rate, 2)
                })
        
        conversion_df = pd.DataFrame(platform_conversion)
        conversion_df = conversion_df.sort_values('总用户数', ascending=False)
        
        print("各平台用户转化情况:")
        print(conversion_df.to_string(index=False))
        
        return conversion_df
    
    def analyze_platform_device_correlation(self):
        """平台设备关联分析"""
        print("\n=== 平台设备关联分析 ===")
        
        # 平台与操作系统关联
        platform_os = pd.crosstab(self.df['via'], self.df['os'])
        platform_os_pct = pd.crosstab(self.df['via'], self.df['os'], normalize='index') * 100
        
        print("平台-操作系统关联:")
        print(platform_os_pct.round(2))
        
        # 平台与浏览器关联
        platform_browser = pd.crosstab(self.df['via'], self.df['browser_name'])
        # 只显示主要浏览器
        main_browsers = platform_browser.sum().nlargest(8).index
        platform_browser_main = platform_browser[main_browsers]
        platform_browser_pct = pd.crosstab(self.df['via'], self.df['browser_name'], normalize='index') * 100
        platform_browser_main_pct = platform_browser_pct[main_browsers]
        
        print("\n平台-主要浏览器关联(%):")
        print(platform_browser_main_pct.round(2))
        
        return platform_os_pct, platform_browser_main_pct
    
    def generate_platform_action_visualizations(self, platform_action_pct, conversion_df):
        """生成平台操作可视化图表"""
        print("\n正在生成平台操作分析图表...")
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('平台操作详细分析报告', fontsize=16, fontweight='bold')
        
        # 1. 各平台操作类型占比堆叠柱状图
        platform_action_pct.plot(kind='bar', stacked=True, ax=axes[0, 0], alpha=0.8)
        axes[0, 0].set_title('各平台操作类型占比')
        axes[0, 0].set_xlabel('平台')
        axes[0, 0].set_ylabel('占比(%)')
        axes[0, 0].legend(title='操作类型', bbox_to_anchor=(1.05, 1), loc='upper left')
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # 2. 各平台用户数对比
        conversion_df_sorted = conversion_df.sort_values('总用户数', ascending=True)
        axes[0, 1].barh(conversion_df_sorted['平台'], conversion_df_sorted['总用户数'])
        axes[0, 1].set_title('各平台用户数对比')
        axes[0, 1].set_xlabel('用户数')
        
        # 3. 各平台转化率对比
        x_pos = range(len(conversion_df))
        width = 0.25
        
        axes[1, 0].bar([x - width for x in x_pos], conversion_df['登录转化率(%)'], 
                      width, label='登录转化率', alpha=0.8)
        axes[1, 0].bar(x_pos, conversion_df['报告转化率(%)'], 
                      width, label='报告转化率', alpha=0.8)
        axes[1, 0].bar([x + width for x in x_pos], conversion_df['KYC转化率(%)'], 
                      width, label='KYC转化率', alpha=0.8)
        
        axes[1, 0].set_title('各平台转化率对比')
        axes[1, 0].set_xlabel('平台')
        axes[1, 0].set_ylabel('转化率(%)')
        axes[1, 0].set_xticks(x_pos)
        axes[1, 0].set_xticklabels(conversion_df['平台'], rotation=45)
        axes[1, 0].legend()
        
        # 4. 平台操作分布饼图（以Android为例）
        if 'Android' in platform_action_pct.index:
            android_actions = platform_action_pct.loc['Android']
            android_actions = android_actions[android_actions > 0]  # 只显示有数据的操作
            axes[1, 1].pie(android_actions.values, labels=android_actions.index, autopct='%1.1f%%')
            axes[1, 1].set_title('Android平台操作分布')
        else:
            # 如果没有Android，显示第一个平台
            first_platform = platform_action_pct.index[0]
            platform_actions = platform_action_pct.loc[first_platform]
            platform_actions = platform_actions[platform_actions > 0]
            axes[1, 1].pie(platform_actions.values, labels=platform_actions.index, autopct='%1.1f%%')
            axes[1, 1].set_title(f'{first_platform}平台操作分布')
        
        plt.tight_layout()
        plt.savefig('平台操作详细分析图表.png', dpi=300, bbox_inches='tight')
        print("平台操作分析图表已保存到: 平台操作详细分析图表.png")
    
    def generate_report(self):
        """生成完整的平台操作分析报告"""
        print("开始生成平台操作详细分析报告...")
        
        # 执行各项分析
        platform_action_crosstab, platform_action_pct, action_platform_pct = self.analyze_platform_actions()
        platform_user_stats = self.analyze_platform_user_behavior()
        daily_platform_action = self.analyze_platform_action_trends()
        conversion_df = self.analyze_platform_conversion()
        platform_os_pct, platform_browser_pct = self.analyze_platform_device_correlation()
        
        # 生成可视化
        self.generate_platform_action_visualizations(platform_action_pct, conversion_df)
        
        # 保存详细数据
        with pd.ExcelWriter('平台操作详细分析数据.xlsx') as writer:
            platform_action_crosstab.to_excel(writer, sheet_name='平台操作交叉表')
            platform_action_pct.to_excel(writer, sheet_name='平台操作占比')
            action_platform_pct.to_excel(writer, sheet_name='操作平台分布')
            platform_user_stats.to_excel(writer, sheet_name='平台用户行为统计')
            daily_platform_action.to_excel(writer, sheet_name='平台操作日趋势')
            conversion_df.to_excel(writer, sheet_name='平台转化分析')
            platform_os_pct.to_excel(writer, sheet_name='平台操作系统关联')
            platform_browser_pct.to_excel(writer, sheet_name='平台浏览器关联')
        
        print("平台操作详细数据已保存到: 平台操作详细分析数据.xlsx")
        
        # 生成总结报告
        self.generate_summary_report(platform_action_pct, conversion_df, platform_user_stats)
    
    def generate_summary_report(self, platform_action_pct, conversion_df, platform_user_stats):
        """生成平台操作分析总结报告"""
        
        # 找出各平台的主要操作
        platform_main_actions = {}
        for platform in platform_action_pct.index:
            main_action = platform_action_pct.loc[platform].idxmax()
            main_action_pct = platform_action_pct.loc[platform].max()
            platform_main_actions[platform] = f"{main_action}({main_action_pct:.1f}%)"
        
        # 找出转化率最高的平台
        best_login_platform = conversion_df.loc[conversion_df['登录转化率(%)'].idxmax(), '平台']
        best_report_platform = conversion_df.loc[conversion_df['报告转化率(%)'].idxmax(), '平台']
        best_kyc_platform = conversion_df.loc[conversion_df['KYC转化率(%)'].idxmax(), '平台']
        
        # 计算总体统计
        total_users = conversion_df['总用户数'].sum()
        total_operations = platform_user_stats['操作次数'].sum()
        
        report = f"""
# 平台操作详细分析总结报告

## 核心发现
- 总用户数: {total_users:,}人
- 总操作次数: {total_operations:,}次
- 覆盖平台数: {len(platform_action_pct)}个

## 各平台主要操作特征
"""
        
        for platform, main_action in platform_main_actions.items():
            report += f"- **{platform}**: {main_action}\n"
        
        report += f"""

## 平台转化率排名
- 登录转化率最高: {best_login_platform}
- 报告转化率最高: {best_report_platform}  
- KYC转化率最高: {best_kyc_platform}

## 平台用户转化详情
{conversion_df.to_string(index=False)}

## 关键洞察
1. 不同平台用户行为模式存在显著差异
2. 移动端平台(Android/iOS)以report操作为主
3. Web平台用户更倾向于进行登录操作
4. KYC转化率在所有平台都相对较低，需要重点优化

## 优化建议
1. 针对各平台特点优化核心功能体验
2. 提升KYC转化率，特别是在主要平台上
3. 加强平台间功能一致性，提升用户体验
4. 基于平台特征制定差异化运营策略

报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        with open('平台操作详细分析总结报告.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print("平台操作分析总结报告已保存到: 平台操作详细分析总结报告.md")
        print(report)

if __name__ == "__main__":
    # 创建平台操作分析器并运行分析
    analyzer = PlatformActionAnalyzer('data/pf调用次数.csv')
    analyzer.generate_report()
