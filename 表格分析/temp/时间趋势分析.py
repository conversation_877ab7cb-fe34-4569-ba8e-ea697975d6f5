#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间趋势分析
基于fp调用数据进行时间维度的深度分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False
except:
    print("警告: 无法设置中文字体，图表可能显示乱码")

class TimeAnalyzer:
    def __init__(self, data_path):
        """初始化时间分析器"""
        self.data_path = data_path
        self.df = None
        self.load_data()
        
    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        try:
            self.df = pd.read_csv(self.data_path)
            print(f"数据加载成功，共{len(self.df)}条记录")
            
            # 时间数据预处理
            self.df['created_time'] = pd.to_datetime(self.df['created_time'])
            self.df['date'] = self.df['created_time'].dt.date
            self.df['hour'] = self.df['created_time'].dt.hour
            self.df['day_of_week'] = self.df['created_time'].dt.dayofweek
            self.df['week'] = self.df['created_time'].dt.isocalendar().week
            self.df['month'] = self.df['created_time'].dt.month
            
            # 添加时间段标签
            self.df['time_period'] = self.df['hour'].apply(self.get_time_period)
            self.df['weekday_name'] = self.df['created_time'].dt.day_name()
            
            print("时间数据预处理完成")
            
        except Exception as e:
            print(f"数据加载失败: {e}")
    
    def get_time_period(self, hour):
        """获取时间段标签"""
        if 6 <= hour < 12:
            return '上午'
        elif 12 <= hour < 18:
            return '下午'
        elif 18 <= hour < 24:
            return '晚上'
        else:
            return '凌晨'
    
    def analyze_daily_trends(self):
        """日趋势分析"""
        print("\n=== 日趋势分析 ===")
        
        # 按日期统计
        daily_stats = self.df.groupby('date').agg({
            'id': 'count',
            'member_id': 'nunique',
            'action_type': lambda x: (x == 'register').sum()
        })
        daily_stats.columns = ['日调用次数', '日活跃用户', '日注册用户']
        daily_stats['人均调用次数'] = daily_stats['日调用次数'] / daily_stats['日活跃用户']
        
        print("日活跃统计:")
        print(daily_stats.describe())
        
        # 找出活跃度最高和最低的日期
        max_day = daily_stats['日活跃用户'].idxmax()
        min_day = daily_stats['日活跃用户'].idxmin()
        
        print(f"\n最活跃日期: {max_day} ({daily_stats.loc[max_day, '日活跃用户']}用户)")
        print(f"最不活跃日期: {min_day} ({daily_stats.loc[min_day, '日活跃用户']}用户)")
        
        return daily_stats
    
    def analyze_hourly_patterns(self):
        """小时模式分析"""
        print("\n=== 小时活跃模式分析 ===")
        
        # 按小时统计
        hourly_stats = self.df.groupby('hour').agg({
            'id': 'count',
            'member_id': 'nunique'
        })
        hourly_stats.columns = ['小时调用次数', '小时活跃用户']
        
        # 按时间段统计
        period_stats = self.df.groupby('time_period').agg({
            'id': 'count',
            'member_id': 'nunique'
        })
        period_stats.columns = ['时段调用次数', '时段活跃用户']
        
        print("时段活跃统计:")
        print(period_stats)
        
        # 找出最活跃的小时
        peak_hour = hourly_stats['小时活跃用户'].idxmax()
        print(f"\n最活跃小时: {peak_hour}点 ({hourly_stats.loc[peak_hour, '小时活跃用户']}用户)")
        
        return hourly_stats, period_stats
    
    def analyze_weekly_patterns(self):
        """周模式分析"""
        print("\n=== 周活跃模式分析 ===")
        
        # 按星期统计
        weekly_stats = self.df.groupby('weekday_name').agg({
            'id': 'count',
            'member_id': 'nunique'
        })
        weekly_stats.columns = ['周调用次数', '周活跃用户']
        
        # 按工作日/周末分类
        self.df['is_weekend'] = self.df['day_of_week'].isin([5, 6])
        weekend_stats = self.df.groupby('is_weekend').agg({
            'id': 'count',
            'member_id': 'nunique'
        })
        weekend_stats.index = ['工作日', '周末']
        weekend_stats.columns = ['调用次数', '活跃用户']
        
        print("工作日vs周末:")
        print(weekend_stats)
        
        print("\n各星期活跃度:")
        print(weekly_stats)
        
        return weekly_stats, weekend_stats
    
    def analyze_user_lifecycle(self):
        """用户生命周期分析"""
        print("\n=== 用户生命周期分析 ===")
        
        # 计算每个用户的首次和最后活跃时间
        user_lifecycle = self.df.groupby('member_id').agg({
            'created_time': ['min', 'max', 'count'],
            'date': 'nunique'
        })
        user_lifecycle.columns = ['首次活跃', '最后活跃', '总调用次数', '活跃天数']
        
        # 计算用户生命周期长度（天）
        user_lifecycle['生命周期天数'] = (
            user_lifecycle['最后活跃'] - user_lifecycle['首次活跃']
        ).dt.days + 1
        
        # 用户留存分析
        user_lifecycle['用户类型'] = user_lifecycle['活跃天数'].apply(
            lambda x: '单日用户' if x == 1 else ('短期用户' if x <= 7 else '长期用户')
        )
        
        user_type_stats = user_lifecycle['用户类型'].value_counts()
        print("用户类型分布:")
        print(user_type_stats)
        
        print("\n生命周期统计:")
        print(user_lifecycle[['生命周期天数', '活跃天数', '总调用次数']].describe())
        
        return user_lifecycle
    
    def analyze_platform_time_patterns(self):
        """平台时间模式分析"""
        print("\n=== 平台时间使用模式 ===")
        
        # 各平台在不同时间段的使用情况
        platform_time = pd.crosstab(self.df['via'], self.df['time_period'])
        platform_time_pct = platform_time.div(platform_time.sum(axis=1), axis=0) * 100
        
        print("各平台时间段使用占比(%):")
        print(platform_time_pct.round(2))
        
        # 各平台周末vs工作日使用情况
        platform_weekend = pd.crosstab(self.df['via'], self.df['is_weekend'])
        platform_weekend.columns = ['工作日', '周末']
        platform_weekend_pct = platform_weekend.div(platform_weekend.sum(axis=1), axis=0) * 100
        
        print("\n各平台工作日vs周末使用占比(%):")
        print(platform_weekend_pct.round(2))
        
        return platform_time_pct, platform_weekend_pct
    
    def generate_time_visualizations(self, daily_stats, hourly_stats, weekly_stats):
        """生成时间趋势可视化"""
        print("\n正在生成时间趋势图表...")
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('时间趋势分析报告', fontsize=16, fontweight='bold')
        
        # 1. 日活跃趋势
        axes[0, 0].plot(daily_stats.index, daily_stats['日活跃用户'], marker='o', linewidth=2)
        axes[0, 0].set_title('日活跃用户趋势')
        axes[0, 0].set_xlabel('日期')
        axes[0, 0].set_ylabel('活跃用户数')
        axes[0, 0].tick_params(axis='x', rotation=45)
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 小时活跃模式
        axes[0, 1].bar(hourly_stats.index, hourly_stats['小时活跃用户'], alpha=0.7)
        axes[0, 1].set_title('24小时活跃用户分布')
        axes[0, 1].set_xlabel('小时')
        axes[0, 1].set_ylabel('活跃用户数')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 星期活跃模式
        week_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        weekly_ordered = weekly_stats.reindex(week_order)
        axes[1, 0].bar(range(len(weekly_ordered)), weekly_ordered['周活跃用户'], alpha=0.7)
        axes[1, 0].set_title('星期活跃用户分布')
        axes[1, 0].set_xlabel('星期')
        axes[1, 0].set_ylabel('活跃用户数')
        axes[1, 0].set_xticks(range(len(weekly_ordered)))
        axes[1, 0].set_xticklabels(['周一', '周二', '周三', '周四', '周五', '周六', '周日'])
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. 日调用量和用户数双轴图
        ax2 = axes[1, 1].twinx()
        line1 = axes[1, 1].plot(daily_stats.index, daily_stats['日调用次数'], 'b-', label='调用次数')
        line2 = ax2.plot(daily_stats.index, daily_stats['日活跃用户'], 'r-', label='活跃用户')
        
        axes[1, 1].set_xlabel('日期')
        axes[1, 1].set_ylabel('调用次数', color='b')
        ax2.set_ylabel('活跃用户数', color='r')
        axes[1, 1].set_title('日调用量与活跃用户关系')
        axes[1, 1].tick_params(axis='x', rotation=45)
        
        # 添加图例
        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        axes[1, 1].legend(lines, labels, loc='upper left')
        
        plt.tight_layout()
        plt.savefig('时间趋势分析图表.png', dpi=300, bbox_inches='tight')
        print("时间趋势图表已保存到: 时间趋势分析图表.png")
    
    def generate_report(self):
        """生成完整时间分析报告"""
        print("开始生成时间趋势分析报告...")
        
        # 执行各项分析
        daily_stats = self.analyze_daily_trends()
        hourly_stats, period_stats = self.analyze_hourly_patterns()
        weekly_stats, weekend_stats = self.analyze_weekly_patterns()
        user_lifecycle = self.analyze_user_lifecycle()
        platform_time_pct, platform_weekend_pct = self.analyze_platform_time_patterns()
        
        # 生成可视化
        self.generate_time_visualizations(daily_stats, hourly_stats, weekly_stats)
        
        # 保存详细数据
        with pd.ExcelWriter('时间趋势分析详细数据.xlsx') as writer:
            daily_stats.to_excel(writer, sheet_name='日趋势')
            hourly_stats.to_excel(writer, sheet_name='小时模式')
            period_stats.to_excel(writer, sheet_name='时段模式')
            weekly_stats.to_excel(writer, sheet_name='星期模式')
            weekend_stats.to_excel(writer, sheet_name='工作日周末')
            user_lifecycle.to_excel(writer, sheet_name='用户生命周期')
            platform_time_pct.to_excel(writer, sheet_name='平台时间模式')
            platform_weekend_pct.to_excel(writer, sheet_name='平台工作日周末')
        
        print("时间趋势详细数据已保存到: 时间趋势分析详细数据.xlsx")
        
        # 生成总结报告
        self.generate_summary_report(daily_stats, period_stats, weekend_stats, user_lifecycle)
    
    def generate_summary_report(self, daily_stats, period_stats, weekend_stats, user_lifecycle):
        """生成时间分析总结报告"""
        
        # 计算关键指标
        avg_daily_users = daily_stats['日活跃用户'].mean()
        peak_period = period_stats['时段活跃用户'].idxmax()
        weekend_ratio = weekend_stats.loc['周末', '活跃用户'] / weekend_stats['活跃用户'].sum() * 100
        single_day_users = len(user_lifecycle[user_lifecycle['用户类型'] == '单日用户'])
        long_term_users = len(user_lifecycle[user_lifecycle['用户类型'] == '长期用户'])
        
        report = f"""
# 时间趋势分析总结报告

## 核心时间指标
- 日均活跃用户: {avg_daily_users:.0f}人
- 最活跃时段: {peak_period}
- 周末用户占比: {weekend_ratio:.1f}%
- 单日用户数: {single_day_users:,}人
- 长期用户数: {long_term_users:,}人

## 时间使用模式
### 时段分布
{period_stats.to_string()}

### 工作日vs周末
{weekend_stats.to_string()}

## 用户留存特征
{user_lifecycle['用户类型'].value_counts().to_string()}

## 关键洞察
1. 用户活跃度在{peak_period}达到峰值
2. 周末用户占比{weekend_ratio:.1f}%，显示用户使用习惯
3. {single_day_users:,}用户仅使用一天，需要提升留存
4. {long_term_users:,}长期用户是核心价值用户

## 优化建议
1. 在{peak_period}时段加强服务器资源配置
2. 针对单日用户制定留存策略
3. 分析长期用户行为模式，复制到其他用户群体
4. 根据时间使用模式优化推送时机

报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        with open('时间趋势分析总结报告.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print("时间趋势总结报告已保存到: 时间趋势分析总结报告.md")
        print(report)

if __name__ == "__main__":
    # 创建时间分析器并运行分析
    analyzer = TimeAnalyzer('data/pf调用次数.csv')
    analyzer.generate_report()
